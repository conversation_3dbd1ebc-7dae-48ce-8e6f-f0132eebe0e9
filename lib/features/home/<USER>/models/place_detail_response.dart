
import 'package:json_annotation/json_annotation.dart';


part 'place_detail_response.g.dart';

@JsonSerializable(explicitToJson: true)
class PlaceDetailResponse {
  String? placeId;
  String? address;
  String? country;
  String? province;
  String? district;
  double? lat;
  double? lng;

  PlaceDetailResponse({
    this.placeId,
    this.address,
    this.country,
    this.province,
    this.district,
    this.lat,
    this.lng,
  });

  factory PlaceDetailResponse.fromJson(Map<String, dynamic> json) =>
      _$PlaceDetailResponseFromJson(json);

  Map<String, dynamic> toJson() => _$PlaceDetailResponseToJson(this);
}

extension PlaceDetailResponseExt on PlaceDetailResponse? {
  bool isSameLocation(PlaceDetailResponse? other) {
    if (this == null) return false;
    if (other == null) return false;
    return this!.lat == other.lat && this!.lng == other.lng;
  }

  bool isNull() {
    return this == null;
  }
}
