import 'package:base_app/core/theme/app_colors.dart';
import 'package:base_app/core/theme/app_text_styles.dart';
import 'package:base_app/core/utils/safe_navigation_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AppBase extends StatelessWidget {
  final Widget body;
  final bool showAppBar;
  final String? titleAppBar;
  final TextStyle? titleStyle;
  final Widget? titleWidget;
  final bool? centerTitle;
  final Color? backgroundColorAppBar;
  final Color? backgroundColor;
  final List<Widget>? actions;
  final Widget? leading;
  final Color? colorIconBack;
  final bool showBackButton;
  final Widget? bottomNavigationBar;
  final double? leadingWidth;
  final Widget? floatingActionButton;
  final FloatingActionButtonLocation? floatingActionButtonLocation;
  final EdgeInsetsGeometry? paddingBody;
  final double? toolbarHeight;
  final Widget? flexibleSpace;
  const AppBase({
    super.key,
    required this.body,
    this.showAppBar = true,
    this.centerTitle = true,
    this.backgroundColorAppBar = AppColors.primary,
    this.backgroundColor,
    this.titleAppBar,
    this.titleWidget,
    this.actions,
    this.leading,
    this.titleStyle,
    this.colorIconBack = AppColors.white,
    this.showBackButton = true,
    this.bottomNavigationBar,
    this.leadingWidth,
    this.floatingActionButton,
    this.floatingActionButtonLocation,
    this.paddingBody,
    this.toolbarHeight,
    this.flexibleSpace,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: backgroundColor ?? AppColors.white,
      bottomNavigationBar: bottomNavigationBar,
      appBar: showAppBar
          ? AppBar(
              flexibleSpace: flexibleSpace,
              toolbarHeight: toolbarHeight,
              leadingWidth: leadingWidth,
              title: titleAppBar != null
                  ? Text(
                      titleAppBar!,
                      style: titleStyle ??
                          AppTextStyle.bold(24.sp)
                              .copyWith(color: AppColors.white),
                    )
                  : titleWidget,
              centerTitle: centerTitle,
              backgroundColor: backgroundColorAppBar,
              scrolledUnderElevation: 0,
              elevation: 0,
              actions: actions,
              leading: leading ??
                  (showBackButton
                      ? IconButton(
                          icon: Icon(
                            Icons.adaptive.arrow_back,
                            color: colorIconBack,
                          ),
                          onPressed: () => SafeNavigationUtil.pop(context),
                        )
                      : null),
            )
          : null,
      floatingActionButton: floatingActionButton,
      floatingActionButtonLocation: floatingActionButtonLocation,
      body: SafeArea(
        child: Padding(
          padding: paddingBody ?? EdgeInsets.zero,
          child: body,
        ),
      ),
    );
  }
}
