// Enum định nghĩa loại SnackBar
import 'package:base_app/core/extension/app_extension_context.dart';
import 'package:base_app/core/theme/app_colors.dart';
import 'package:base_app/core/translation/app_string.dart';
import 'package:flutter/material.dart';

enum SnackBarType { success, warning, error, info }

extension SnackBarTypeExtension on SnackBarType {
  Color get color {
    switch (this) {
      case SnackBarType.success:
        return AppColors.mantis;
      case SnackBarType.warning:
        return AppColors.maximumYellowRed;
      case SnackBarType.error:
        return AppColors.red;
      case SnackBarType.info:
        return AppColors.honoluluBlue;
    }
  }

  IconData get icon {
    switch (this) {
      case SnackBarType.success:
        return Icons.check_circle;
      case SnackBarType.warning:
        return Icons.warning;
      case SnackBarType.error:
        return Icons.error;
      case SnackBarType.info:
        return Icons.info;
    }
  }
}

class AppSnackBar {
  static void showCustom(
    BuildContext context,
    String? message,
    SnackBarType type, {
    Duration duration = const Duration(seconds: 3),
    bool showInBottom = false,
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        backgroundColor: type.color,
        duration: duration,
        shape: RoundedRectangleBorder(
          borderRadius: context.borderRadiusMedium,
        ),
        behavior: SnackBarBehavior.floating,
        content: Align(
          alignment: Alignment.topCenter,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(type.icon, color: AppColors.white),
              context.horizontalSpaceSmall,
              Expanded(
                child: Text(
                  message ?? AppString.defaultError,
                  style: const TextStyle(color: AppColors.white),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  static void success(
    BuildContext context, {
    String? message,
    bool showInBottom = false,
  }) =>
      showCustom(
        context,
        message,
        SnackBarType.success,
        showInBottom: showInBottom,
      );

  static void warning(
    BuildContext context, {
    String? message,
    bool showInBottom = false,
  }) =>
      showCustom(
        context,
        message,
        SnackBarType.warning,
        showInBottom: showInBottom,
      );

  static void error(
    BuildContext context, {
    String? message,
    bool showInBottom = false,
  }) =>
      showCustom(
        context,
        message,
        SnackBarType.error,
        showInBottom: showInBottom,
      );

  static void info(
    BuildContext context, {
    String? message,
    bool showInBottom = false,
  }) =>
      showCustom(
        context,
        message,
        SnackBarType.info,
        showInBottom: showInBottom,
      );
}
