import 'package:base_app/core/extension/app_extension_string.dart';
import 'package:base_app/core/translation/app_language.dart';

class AppString {
  AppString._();
  static String get defaultError => AppLanguage.errorDefault.tr;
  static String get phoneNumber => AppLanguage.phoneNumber.tr;
  static String get enterPhoneNumber => AppLanguage.enterPhoneNumber.tr;
  static String get password => AppLanguage.password.tr;
  static String get enterPassword => AppLanguage.enterPassword.tr;
  static String get login => AppLanguage.login.tr;
  static String get logout => AppLanguage.logout.tr;
  static String get rememberMe => AppLanguage.rememberMe.tr;
  static String get continueText => AppLanguage.continueText.tr;
  static String get contactCustomerCare => AppLanguage.contactCustomerCare.tr;
  static String get verifyCapcha => AppLanguage.verifyCapcha.tr;
  static String get invalidPhoneNumber => AppLanguage.invalidPhoneNumber.tr;
  static String get tooManyRequests => AppLanguage.tooManyRequests.tr;
  static String get verificationFailed => AppLanguage.verificationFailed.tr;
  static String get resendOTP => AppLanguage.resendOTP.tr;
  static String get enterOTP => AppLanguage.enterOTP.tr;
  static String get verifyOTP => AppLanguage.verifyOTP.tr;
  static String get verifyOTPSendToPhone => AppLanguage.verifyOTPSendToPhone.tr;
  static String get didNotReceiveOTP => AppLanguage.didNotReceiveOTP.tr;
  static String resendSeconds(int seconds) =>
      AppLanguage.resendSeconds.trParams({'seconds': seconds.toString()});
  static String get invalidOtpCode => AppLanguage.invalidOtpCode.tr;
  static String get sessionExpired => AppLanguage.sessionExpired.tr;
  static String get invalidVerificationId =>
      AppLanguage.invalidVerificationId.tr;
  static String get networkError => AppLanguage.networkError.tr;
  static String get unknownError => AppLanguage.unknownError.tr;
  static String get invalidPhoneNumberError =>
      AppLanguage.invalidPhoneNumberError.tr;
  static String get tooManyRequestsError => AppLanguage.tooManyRequestsError.tr;
  static String get verificationFailedError =>
      AppLanguage.verificationFailedError.tr;
  static String get enterPoint => AppLanguage.enterPoint.tr;
  static String get enterChooseUser => AppLanguage.enterChooseUser.tr;
  static String get enterEmail => AppLanguage.enterEmail.tr;
  static String get enterPhone => AppLanguage.enterPhone.tr;
  static String get invalidEmail => AppLanguage.invalidEmail.tr;
  static String get enterValidPhone => AppLanguage.enterValidPhone.tr;
  static String get invalidPoint => AppLanguage.invalidPoint.tr;
  static String get pointMustBePositive => AppLanguage.pointMustBePositive.tr;
  static String get onlyAcceptSpecificValues =>
      AppLanguage.onlyAcceptSpecificValues.tr;
  static String pointCannotExceedMaximum(String maxPoint) =>
      AppLanguage.pointCannotExceedMaximum.trParams({'point': maxPoint});
  static String get home => AppLanguage.home.tr;
  static String get activity => AppLanguage.activity.tr;
  static String get profile => AppLanguage.profile.tr;
  static String get onGoing => AppLanguage.onGoing.tr;
  static String get history => AppLanguage.history.tr;
  static String get detail => AppLanguage.detail.tr;
  static String get orderNumber => AppLanguage.orderNumber.tr;
  static String get cancel => AppLanguage.cancel.tr;
  static String get confirm => AppLanguage.confirm.tr;
  static String get noInternetTitle => AppLanguage.noInternetTitle.tr;
  static String get noInternetContent => AppLanguage.noInternetContent.tr;
  static String get startPoint => AppLanguage.startPoint.tr;
  static String get endPoint => AppLanguage.endPoint.tr;
  static String get enterSeat => AppLanguage.enterSeat.tr;
  static String get edit => AppLanguage.edit.tr;
  static String get create => AppLanguage.create.tr;
  static String get pickUpPlace => AppLanguage.pickUpPlace.tr;
  static String get destinationPlace => AppLanguage.destinationPlace.tr;
  static String get pleasePickUpPlace => AppLanguage.pleasePickUpPlace.tr;
  static String get pleaseDestinationPlace =>
      AppLanguage.pleaseDestinationPlace.tr;
  static String get transport => AppLanguage.transport.tr;
  static String get openMap => AppLanguage.openMap.tr;
  static String get titleTransport => AppLanguage.titleTransport.tr;
  static String get whereTo => AppLanguage.whereTo.tr;
  static String get titleHome => AppLanguage.titleHome.tr;
  static String get recentPlace => AppLanguage.recentPlace.tr;
  static String get userRegistration => AppLanguage.userRegistration.tr;
  static String get confirmPassword => AppLanguage.confirmPassword.tr;
  static String get passwordNotMatch => AppLanguage.passwordNotMatch.tr;
  static String get enterConfirmPassword => AppLanguage.enterConfirmPassword.tr;
  static String get email => AppLanguage.email.tr;
  static String get fullName => AppLanguage.fullName.tr;
  static String get enterFullName => AppLanguage.enterFullName.tr;
  static String get register => AppLanguage.register.tr;
  static String get currentLocation => AppLanguage.currentLocation.tr;
  static String get destinationLocation => AppLanguage.destinationLocation.tr;
  static String get searchPlace => AppLanguage.searchPlace.tr;
  static String get listSearchPlace => AppLanguage.listSearchPlace.tr;
  static String get pleaseSelectStartPoint =>
      AppLanguage.pleaseSelectStartPoint.tr;
  static String get pleaseSelectEndPoint => AppLanguage.pleaseSelectEndPoint.tr;
  static String get bookTrip => AppLanguage.bookTrip.tr;
}
