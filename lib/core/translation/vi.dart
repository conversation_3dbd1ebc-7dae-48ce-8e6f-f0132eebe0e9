import 'app_language.dart';

Map<String, String> viLanguage = {
  AppLanguage.errorDefault: 'Đã có lỗi xảy ra',
  AppLanguage.phoneNumber: 'Số điện thoại',
  AppLanguage.enterPhoneNumber: '<PERSON>hập số điện thoại của bạn',
  AppLanguage.password: '<PERSON><PERSON><PERSON> khẩu',
  AppLanguage.enterPassword: 'Nhập mật khẩu của bạn',
  AppLanguage.login: '<PERSON>ă<PERSON> nhập',
  AppLanguage.logout: 'Đăng xuất',
  AppLanguage.rememberMe: 'Ghi nhớ đăng nhập',
  AppLanguage.continueText: 'Tiếp tục',
  AppLanguage.contactCustomerCare: "Liên hệ chăm sóc khách hàng",
  AppLanguage.verifyCapcha: "<PERSON>ui lòng xác thực capcha",
  AppLanguage.invalidPhoneNumber: "<PERSON>ố điện thoại không hợp lệ",
  AppLanguage.tooManyRequests:
      "<PERSON>u<PERSON> nhiều yêu cầu. Vui lòng thử lại sau hoặc liên hệ với bộ phận hỗ trợ.",
  AppLanguage.verificationFailed: "Xác thực thất bại",
  AppLanguage.resendOTP: "Gửi lại OTP",
  AppLanguage.enterOTP: "Nhập OTP",
  AppLanguage.verifyOTP: "Xác thực OTP",
  AppLanguage.verifyOTPSendToPhone: "Xác thực OTP đã gửi đến số điện thoại",
  AppLanguage.didNotReceiveOTP: "Không nhận được OTP?",
  AppLanguage.resendSeconds: "Gửi lại trong @seconds giây",
  AppLanguage.invalidOtpCode: 'Mã OTP không hợp lệ. Vui lòng thử lại.',
  AppLanguage.sessionExpired:
      'Phiên xác thực đã hết hạn. Vui lòng yêu cầu gửi lại mã OTP.',
  AppLanguage.invalidVerificationId:
      'Mã xác thực không hợp lệ hoặc đã hết hạn. Vui lòng thử lại.',
  AppLanguage.networkError: 'Lỗi mạng. Vui lòng kiểm tra kết nối internet.',
  AppLanguage.unknownError: 'Đã xảy ra lỗi không xác định',
  AppLanguage.invalidPhoneNumberError: 'Số điện thoại không hợp lệ',
  AppLanguage.tooManyRequestsError:
      'Quá nhiều request, xin vui lòng thử lại sau',
  AppLanguage.verificationFailedError: 'Xác thực thất bại',
  AppLanguage.enterPoint: "Vui lòng nhập điểm!",
  AppLanguage.enterChooseUser: "Vui lòng chọn người dùng!",
  AppLanguage.enterEmail: "Vui lòng nhập email!",
  AppLanguage.enterPhone: "Vui lòng nhập số điện thoại!",
  AppLanguage.invalidEmail: "Vui lòng nhập đúng định dạng email!",
  AppLanguage.enterValidPhone: "Vui lòng nhập đúng định dạng số điện thoại!",
  AppLanguage.invalidPoint: "Số điểm không hợp lệ",
  AppLanguage.pointMustBePositive: "Số điểm không được âm",
  AppLanguage.onlyAcceptSpecificValues: "Chỉ nhập 0, 0.25, 0.5, 0.75…",
  AppLanguage.pointCannotExceedMaximum:
      "Số điểm không được vượt quá @point điểm",
  AppLanguage.home: "Trang chủ",
  AppLanguage.activity: "Hoạt động",
  AppLanguage.profile: "Cá nhân",
  AppLanguage.onGoing: "Đang thực hiện",
  AppLanguage.history: "Lịch sử",
  AppLanguage.detail: "Chi tiết",
  AppLanguage.orderNumber: "Số đơn hàng",
  AppLanguage.cancel: "Hủy",
  AppLanguage.confirm: "Xác nhận",
  AppLanguage.noInternetTitle: "Không có kết nối Internet",
  AppLanguage.noInternetContent:
      "Vui lòng kiểm tra kết nối Internet của bạn và thử lại.",
  AppLanguage.startPoint: "Vui lòng chọn điểm đón!",
  AppLanguage.endPoint: "Vui lòng chọn điểm trả!",
  AppLanguage.enterSeat: "Vui lòng chọn số chỗ!",
  AppLanguage.edit: "Chỉnh sửa",
  AppLanguage.create: "Tạo mới",
  AppLanguage.pickUpPlace: "Điểm đón",
  AppLanguage.destinationPlace: "Điểm đến",
  AppLanguage.pleasePickUpPlace: "Vui lòng chọn điểm đón!",
  AppLanguage.pleaseDestinationPlace: "Vui lòng chọn điểm đến!",
  AppLanguage.transport: "Giao hàng",
  AppLanguage.openMap: "Mở bản đồ",
  AppLanguage.titleTransport: "Đến bất cứ đâu, chúng tôi sẽ đưa bạn đến đó!",
  AppLanguage.whereTo: "Đến đâu?",
  AppLanguage.titleHome: "Chào mừng bạn",
  AppLanguage.recentPlace: "Địa điểm gần đây",
  AppLanguage.userRegistration: "Đăng ký tài khoản",
  AppLanguage.confirmPassword: "Xác nhận mật khẩu",
  AppLanguage.passwordNotMatch: "Mật khẩu không khớp",
  AppLanguage.enterConfirmPassword: "Vui lòng nhập xác nhận mật khẩu!",
  AppLanguage.email: "Email",
  AppLanguage.fullName: "Họ và tên",
  AppLanguage.enterFullName: "Vui lòng nhập họ và tên!",
  AppLanguage.register: "Đăng ký",
  AppLanguage.currentLocation: "Vị trí hiện tại",
  AppLanguage.destinationLocation: "Điểm đến",
  AppLanguage.searchPlace: "Tìm kiếm địa điểm",
  AppLanguage.listSearchPlace: "Danh sách tìm kiếm",
  AppLanguage.pleaseSelectStartPoint: "Vui lòng chọn điểm đón!",
  AppLanguage.pleaseSelectEndPoint: "Vui lòng chọn điểm đến!",
  AppLanguage.bookTrip: "Đặt chuyến",
};
